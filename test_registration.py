#!/usr/bin/env python3
"""
Test script to verify the registration endpoint is working after logging fixes.
"""

import requests
import json

def test_registration():
    """Test the registration endpoint with sample data."""

    url = "http://127.0.0.1:8000/api/user/register/"

    # Sample registration data
    data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "name": "Test User",  # Added required name field
        "device_type": "web"
    }

    headers = {
        "Content-Type": "application/json",
        "User-Agent": "TestClient/1.0"
    }

    try:
        print("🧪 Testing registration endpoint...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(data, indent=2)}")

        response = requests.post(url, json=data, headers=headers, timeout=10)

        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")

        try:
            response_data = response.json()
            print(f"📄 Response Body: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📄 Response Body (raw): {response.text}")

        if response.status_code == 500:
            print("❌ Server error detected - check logs for details")
            return False
        elif response.status_code in [200, 201]:
            print("✅ Registration successful!")
            return True
        elif response.status_code == 400:
            print("⚠️  Validation error (expected for duplicate email)")
            return True
        else:
            print(f"ℹ️  Unexpected status code: {response.status_code}")
            return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    test_registration()
