#!/usr/bin/env python3
"""
Test script to verify the logging configuration with colorlog.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django with DEBUG=True to enable colorlog
os.environ["DEBUG"] = "True"
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings")
django.setup()

import logging


def test_logging():
    """Test the logging configuration"""

    print("🎨 Testing Logging Configuration")
    print("=" * 50)

    # Test different loggers
    main_logger = logging.getLogger(__name__)
    user_logger = logging.getLogger("user.services.registration_orchestrator")
    security_logger = logging.getLogger("security")
    agritram_logger = logging.getLogger("agritram")

    print("\n📝 Testing main logger:")
    main_logger.debug("This is a DEBUG message")
    main_logger.info("This is an INFO message")
    main_logger.warning("This is a WARNING message")
    main_logger.error("This is an ERROR message")
    main_logger.critical("This is a CRITICAL message")

    print("\n👤 Testing user service logger:")
    user_logger.debug("User registration debug message")
    user_logger.info(
        "User registration info message",
        extra={
            "operation": "REGISTRATION_TEST",
            "email": "<EMAIL>",
            "user_id": 123,
        },
    )
    user_logger.warning("User registration warning message")
    user_logger.error("User registration error message")

    print("\n🔒 Testing security logger:")
    security_logger.warning("Security warning message")
    security_logger.error("Security error message")
    security_logger.critical("Security critical message")

    print("\n🌾 Testing agritram logger:")
    agritram_logger.info("Agritram info message")
    agritram_logger.warning("Agritram warning message")
    agritram_logger.error("Agritram error message")

    print("\n" + "=" * 50)
    print("✅ Logging test completed!")
    print("🎨 If DEBUG=True, you should see colored output.")
    print("📁 Logs are also written to files in the logs/ directory.")


if __name__ == "__main__":
    test_logging()
