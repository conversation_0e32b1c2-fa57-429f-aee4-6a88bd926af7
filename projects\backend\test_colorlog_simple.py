#!/usr/bin/env python3
"""
Simple test script to verify colorlog works without Django.
"""

import logging
import colorlog

def test_simple_colorlog():
    """Test colorlog with a simple configuration"""
    
    # Create a colored formatter
    formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(levelname)-8s%(reset)s | %(cyan)s%(asctime)s%(reset)s | %(blue)s%(filename)s:%(lineno)d%(reset)s | %(log_color)s%(message)s%(reset)s",
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # Create a handler
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    
    # Create a logger
    logger = logging.getLogger('test_colorlog')
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    
    print("🎨 Testing Simple Colorlog Setup")
    print("=" * 50)
    
    # Test different log levels
    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")
    
    print("=" * 50)
    print("✅ Simple colorlog test completed!")
    print("If you see colored output above, colorlog is working correctly.")

if __name__ == "__main__":
    test_simple_colorlog()
