"""
Django settings for agritram project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-s&ys=62#!6uiz5%_hdr%mt4!jfi5++#5ze@is5j*!(-o9w=-3i"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework.authtoken",
    "corsheaders",
    "oauth2_provider",
    "django_extensions",
    "oauth2_auth",
    "user",
    "news",
    "transactions",
    "crops",
    "inventory",
    "proof_of_unlock",
]

APPEND_SLASH = False

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "agritram.correlation_id_middleware.CorrelationIdMiddleware",  # Correlation ID tracking
    "oauth2_auth.custom_cors_middleware.CustomCorsMiddleware",  # Custom CORS for security headers
    "corsheaders.middleware.CorsMiddleware",
    "oauth2_auth.rate_limiting_service.RateLimitMiddleware",
    "oauth2_auth.security.IPWhitelistMiddleware",
    "oauth2_auth.security.RequestValidationMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "oauth2_provider.middleware.OAuth2TokenMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "oauth2_auth.device_security_middleware.DeviceSecurityMiddleware",  # Device security validation
    "user.middleware.UserStatusValidationMiddleware",
    "user.middleware.UserStatusAuditMiddleware",
    "oauth2_auth.security.AuditLoggingMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "agritram.urls"

CORS_ALLOWED_ORIGINS = [
    config("FARMER_FRONTEND_URL"),
    config("TRADER_FRONTEND_URL"),
    config("MANUFACTURER_FRONTEND_URL"),
    # Allow localhost and 127.0.0.1 variations for development
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5174",
    "http://localhost:5175",
    "http://127.0.0.1:5175",
]

# Enhanced CORS settings for security
CORS_ALLOW_CREDENTIALS = True
# Allow all headers for development (more permissive)
CORS_ALLOW_ALL_ORIGINS = False  # Keep origin restrictions

# Use the most permissive setting for development
CORS_ALLOW_ALL_HEADERS = True

# Additional CORS settings
CORS_ALLOWED_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

# Force preflight for all requests
CORS_PREFLIGHT_MAX_AGE = 86400
CORS_EXPOSE_HEADERS = [
    "x-ratelimit-limit",
    "x-ratelimit-remaining",
    "x-ratelimit-reset",
]
CORS_PREFLIGHT_MAX_AGE = config(
    "CORS_PREFLIGHT_MAX_AGE", default=86400, cast=int
)  # 24 hours

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "oauth2_auth.authentication.OAuth2Authentication",
        "oauth2_auth.authentication.EnhancedSessionAuthentication",
        "oauth2_auth.authentication.EnhancedTokenAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "EXCEPTION_HANDLER": "agritram.exception_handlers.custom_exception_handler",
}

AUTHENTICATION_BACKENDS = [
    "oauth2_provider.backends.OAuth2Backend",
    "user.backends.EmailBackend",
    "django.contrib.auth.backends.ModelBackend",
]

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = config("EMAIL_PORT", default=465, cast=int)
EMAIL_USE_TLS = False
EMAIL_USE_SSL = True
EMAIL_HOST_USER = config("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD")
SUPPORT_EMAIL = "<EMAIL>"
FARMER_FRONTEND_URL = config("FARMER_FRONTEND_URL")
TRADER_FRONTEND_URL = config("TRADER_FRONTEND_URL")
MANUFACTURER_FRONTEND_URL = config("MANUFACTURER_FRONTEND_URL")
FRONTEND_URL = config("FRONTEND_URL")

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "agritram.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("DATABASE_NAME"),
        "USER": config("DATABASE_USER"),
        "PASSWORD": config("DATABASE_PASSWORD"),
        "HOST": config("DATABASE_IP"),
        "PORT": config("DATABASE_PORT"),
        "OPTIONS": {
            "options": "-c search_path=public",
        },
    }
}

AUTH_USER_MODEL = "user.User"


# Password hashing
# https://docs.djangoproject.com/en/5.2/topics/auth/passwords/#using-argon2-with-django

PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
    "django.contrib.auth.hashers.ScryptPasswordHasher",
]

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# ============================================================================
# OAuth2 Settings
# ============================================================================

OAUTH2_PROVIDER = {
    # OAuth2 scopes
    "SCOPES": {
        "read": "Read scope",
        "write": "Write scope",
        "profile": "Access to user profile",
        "email": "Access to user email",
        "openid": "OpenID Connect scope",
        "farmer": "Farmer role access",
        "trader": "Trader role access",
        "manufacturer": "Manufacturer role access",
        "admin": "Admin role access",
    },
    # Token settings - use centralized configuration
    "ACCESS_TOKEN_EXPIRE_SECONDS": config(
        "OAUTH2_ACCESS_TOKEN_EXPIRE_SECONDS", default=3600, cast=int
    ),  # 1 hour
    "REFRESH_TOKEN_EXPIRE_SECONDS": config(
        "OAUTH2_REFRESH_TOKEN_EXPIRE_SECONDS", default=604800, cast=int
    ),  # 1 week
    "AUTHORIZATION_CODE_EXPIRE_SECONDS": config(
        "OAUTH2_AUTHORIZATION_CODE_EXPIRE_SECONDS", default=600, cast=int
    ),  # 10 minutes
    # Security settings
    "ROTATE_REFRESH_TOKEN": True,
    "PKCE_REQUIRED": True,  # Require PKCE for all clients
    "OIDC_ENABLED": True,
    "OIDC_RSA_PRIVATE_KEY": config("OIDC_RSA_PRIVATE_KEY", default=""),
    # Application settings (using default models)
    "APPLICATION_MODEL": "oauth2_provider.Application",
    "ACCESS_TOKEN_MODEL": "oauth2_provider.AccessToken",
    "REFRESH_TOKEN_MODEL": "oauth2_provider.RefreshToken",
    "GRANT_MODEL": "oauth2_provider.Grant",
    "ID_TOKEN_MODEL": "oauth2_provider.IDToken",
    # Error handling
    "ERROR_RESPONSE_WITH_SCOPES": True,
    # Request validation
    "REQUEST_APPROVAL_PROMPT": "auto",
}

# JWT Settings are handled by OAuth2 provider

# Security headers and CORS
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = "DENY"

# Session security
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = "Lax"
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = "Lax"

# Additional security settings for fintech
SECURE_HSTS_SECONDS = (
    config("SECURE_HSTS_SECONDS", default=31536000, cast=int) if not DEBUG else 0
)
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Request size limits
MAX_REQUEST_SIZE = config(
    "MAX_REQUEST_SIZE", default=10485760, cast=int
)  # 10MB in bytes

# Correlation ID settings
CORRELATION_ID_RESPONSE_HEADER = "X-Correlation-ID"
CORRELATION_ID_GENERATE_IF_NOT_EXISTS = True
CORRELATION_ID_EXCLUDED_PATHS = [
    "/static/",
    "/media/",
    "/health/",
    "/favicon.ico",
    "/admin/jsi18n/",
]

# IP whitelist/blacklist (configure as needed)
WHITELISTED_IPS = []  # Add trusted IP ranges
BLACKLISTED_IPS = []  # Add blocked IP ranges

# API versioning and deprecation
API_VERSION = "v1"
API_DEPRECATION_WARNINGS = True

# Content Security Policy
CSP_DEFAULT_SRC = ["'self'"]
CSP_SCRIPT_SRC = ["'self'", "'unsafe-inline'"]
CSP_STYLE_SRC = ["'self'", "'unsafe-inline'"]
CSP_IMG_SRC = ["'self'", "data:", "https:"]
CSP_CONNECT_SRC = ["'self'"]
CSP_FONT_SRC = ["'self'"]
CSP_OBJECT_SRC = ["'none'"]
CSP_MEDIA_SRC = ["'self'"]
CSP_FRAME_SRC = ["'none'"]

# Logging configuration for security events
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "{levelname} | {asctime} | {name} | {filename}:{lineno} | {funcName} | CID:{correlation_id} | {message}",
            "style": "{",
        },
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} CID:{correlation_id} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} CID:{correlation_id} {message}",
            "style": "{",
        },
        "api_format": {
            "format": "{levelname} | {asctime} | {filename}:{lineno} | CID:{correlation_id} | {message}",
            "style": "{",
        },
        "correlation_format": {
            "format": "{levelname} | {asctime} | {filename}:{lineno} | CID:{correlation_id} | {message}",
            "style": "{",
        },
        "colored": {
            "()": "colorlog.ColoredFormatter",
            "format": "{log_color}{levelname:<8}{reset} | {cyan}{asctime}{reset} | {blue}{filename}:{lineno}{reset} | {purple}CID:{correlation_id}{reset} | {log_color}{message}{reset}",
            "style": "{",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
            "secondary_log_colors": {
                "message": {
                    "DEBUG": "white",
                    "INFO": "white",
                    "WARNING": "white",
                    "ERROR": "white",
                    "CRITICAL": "white",
                }
            },
        },
    },
    "filters": {
        "correlation_id": {
            "()": "agritram.correlation_id_middleware.CorrelationIdLoggingFilter",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": "logs/security.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "colored" if DEBUG else "api_format",
            "filters": ["correlation_id"],
        },
        "console_dev": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "colored",
        },
        "api_file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": "logs/api.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "logs/errors.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
    },
    "loggers": {
        "oauth2_auth": {
            "handlers": ["file", "console"],
            "level": "INFO",
            "propagate": True,
        },
        "security": {
            "handlers": ["file", "console"],
            "level": "WARNING",
            "propagate": True,
        },
        "agritram": {
            "handlers": ["api_file", "console"],
            "level": "INFO",
            "propagate": True,
        },
        "user": {
            "handlers": ["api_file", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": True,
        },
        "user.services": {
            "handlers": ["api_file", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": True,
        },
        "django": {
            "handlers": ["error_file", "console"],
            "level": "ERROR",
            "propagate": False,
        },
    },
}
