#!/usr/bin/env python3
"""
Test script to verify colorlog setup is working correctly.
Run this script to see colored log output in your terminal.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django with development settings
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings_dev")
django.setup()

import logging
import uuid

# Test different loggers
logger = logging.getLogger(__name__)
user_logger = logging.getLogger("user.services.registration_orchestrator")
security_logger = logging.getLogger("security")
agritram_logger = logging.getLogger("agritram")


def test_colorlog():
    """Test colorlog with different log levels and loggers"""

    print("🎨 Testing Colorlog Setup")
    print("=" * 50)

    # Generate a test correlation ID
    test_correlation_id = str(uuid.uuid4())[:8]

    # Test main logger
    print("\n📝 Testing main logger:")
    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")

    # Test user service logger
    print("\n👤 Testing user service logger:")
    user_logger.debug("User registration debug message")
    user_logger.info(
        "User registration info message",
        extra={
            "operation": "REGISTRATION_TEST",
            "email": "<EMAIL>",
            "user_id": 123,
        },
    )
    user_logger.warning("User registration warning message")
    user_logger.error("User registration error message")

    # Test security logger
    print("\n🔒 Testing security logger:")
    security_logger.warning("Security warning message")
    security_logger.error("Security error message")
    security_logger.critical("Security critical message")

    # Test agritram logger
    print("\n🌾 Testing agritram logger:")
    agritram_logger.info("Agritram info message")
    agritram_logger.warning("Agritram warning message")
    agritram_logger.error("Agritram error message")

    print("\n" + "=" * 50)
    print("✅ Colorlog test completed!")
    print("If you see colored output above, colorlog is working correctly.")
    print("If not, check your terminal supports colors and colorlog is installed.")


if __name__ == "__main__":
    test_colorlog()
