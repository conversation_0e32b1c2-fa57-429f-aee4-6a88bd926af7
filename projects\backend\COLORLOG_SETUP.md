# Colorlog Setup for Local Development

This document explains how to use the enhanced logging setup with colorlog for better local development experience.

## Overview

The project now includes colorlog support for colored console output during development. This makes it easier to distinguish between different log levels and quickly identify important messages.

## Setup Options

### Option 1: Using Development Settings (Recommended)

Use the dedicated development settings file that includes enhanced logging:

```bash
# Set environment variable
export DJANGO_SETTINGS_MODULE=agritram.settings_dev

# Or run commands directly with development settings
python manage.py runserver --settings=agritram.settings_dev
python manage.py shell --settings=agritram.settings_dev
```

### Option 2: Using Regular Settings with DEBUG=True

The regular settings file will automatically use colorlog when `DEBUG=True`:

```bash
# Make sure DEBUG=True in your .env file
echo "DEBUG=True" >> .env

# Run normally
python manage.py runserver
```

## Features

### Enhanced Logging Format

The colorlog setup provides:

- **Color-coded log levels**: Different colors for DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Structured format**: Level | Timestamp | File:Line | Correlation ID | Message
- **Debug logging**: Enhanced debug output for user registration and related services
- **File logging**: Logs are also written to files in the `logs/` directory

### Log Level Colors

- **DEBUG**: Cyan
- **INFO**: Green  
- **WARNING**: Yellow
- **ERROR**: Red
- **CRITICAL**: Red with white background

### Enhanced Registration Logging

The registration function now includes:

1. **Request tracking**: Unique request ID for each registration attempt
2. **Debug logs**: Detailed information about request processing
3. **Structured logging**: Consistent format with operation types and metadata
4. **Client information**: IP address, user agent, and device fingerprint logging
5. **Step-by-step tracking**: Each registration step is logged with clear operation names

## Testing the Setup

### Test Colorlog Configuration

Run the test script to verify colorlog is working:

```bash
python test_colorlog.py
```

### Test Simple Colorlog (without Django)

Run a simple test without Django dependencies:

```bash
python test_colorlog_simple.py
```

## Log Files

When using development settings, logs are written to:

- `logs/development.log` - All log messages
- `logs/api_dev.log` - API and user service logs
- `logs/errors_dev.log` - Error level and above

## Troubleshooting

### Colors Not Showing

If you don't see colors in your terminal:

1. **Check terminal support**: Ensure your terminal supports ANSI color codes
2. **Windows users**: Use Windows Terminal, PowerShell, or WSL for better color support
3. **IDE terminals**: Some IDE terminals may not support colors properly
4. **Force colors**: Set environment variable `FORCE_COLOR=1`

### Log Level Issues

If you're not seeing debug logs:

1. **Check DEBUG setting**: Ensure `DEBUG=True` in your environment
2. **Check log level**: Verify logger level is set to DEBUG
3. **Check handlers**: Ensure console handler is configured correctly

### Performance Considerations

- **Development only**: Colorlog adds slight overhead, use only in development
- **Log level**: Set appropriate log levels to avoid excessive output
- **File logging**: Monitor log file sizes in development

## Example Output

When working correctly, you should see output like:

```
DEBUG    | 2025-07-29 15:30:06,681 | registration_orchestrator.py:67 | CID:API_c7000583 | Registration request details
INFO     | 2025-07-29 15:30:06,682 | registration_orchestrator.py:95 | CID:API_c7000583 | Starting registration validation  
WARNING  | 2025-07-29 15:30:06,682 | user_creation_service.py:80 | CID:API_c7000583 | User creation failed due to validation errors
ERROR    | 2025-07-29 15:30:06,682 | registration_orchestrator.py:206 | CID:API_c7000583 | Unexpected registration error
```

## Integration with Existing Code

The colorlog setup integrates seamlessly with:

- **Django correlation ID middleware**: Maintains correlation tracking
- **Existing loggers**: All current loggers continue to work
- **File logging**: Maintains file-based logging for production compatibility
- **Security logging**: Preserves security event logging

## Next Steps

1. **Test the setup**: Run the test scripts to verify everything works
2. **Use development settings**: Switch to `settings_dev.py` for local development
3. **Monitor logs**: Check both console and file outputs
4. **Customize as needed**: Adjust colors or formats in the settings file
