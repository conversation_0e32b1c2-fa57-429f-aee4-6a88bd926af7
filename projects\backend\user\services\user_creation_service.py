"""
User Creation Service

This service handles all user creation logic including:
- User serialization and validation
- OAuth2 application creation
- User saving and setup
"""

from typing import Dict, Any, Tuple, Optional
import secrets
from oauth2_provider.models import Application
from agritram.message_utils import (
    get_frontend_url_by_role,
    detect_user_role_from_request,
    handle_registration_serializer_errors,
)
from user.models import User
from user.serializers import UserSerializer
import logging, json

logger = logging.getLogger(__name__)


class UserCreationService:
    """Service for handling user creation and setup during registration"""

    @staticmethod
    def prepare_registration_data(request) -> Dict[str, Any]:
        """
        Prepare registration data with auto-detected role

        Args:
            request: HTTP request object

        Returns:
            Dict containing prepared registration data
        """
        # Auto-detect user role from request origin
        user_role = detect_user_role_from_request(request)

        # Add the detected role to the request data
        registration_data = request.data.copy()
        registration_data["role"] = user_role

        return registration_data

    @staticmethod
    def create_user(
        registration_data: Dict[str, Any]
    ) -> <PERSON><PERSON>[Optional[User], Optional[UserSerializer], bool]:
        """
        Create and validate user using serializer

        Args:
            registration_data: Registration data dictionary

        Returns:
            Tuple of (user: Optional[User], serializer: Optional[UserSerializer], is_valid: bool)
        """
        serializer = UserSerializer(data=registration_data)

        if serializer.is_valid():
            # Create user (inactive by default)
            user = serializer.save()

            logger.info(
                f"OPERATION_INFO: USER_CREATION | message=User created successfully: {user.email} | metadata="
                f"{{'user_id': {user.id}, 'user_email': '{user.email}', 'user_role': '{user.role}', 'user_name': '{user.name}'}}"
            )

            return user, serializer, True
        else:
            logger.warning(
                f"OPERATION_INFO: USER_CREATION_VALIDATION_ERROR | message=User creation failed due to validation errors | metadata="
                f"{{'validation_errors': {json.dumps(serializer.errors)}, 'registration_data': {json.dumps({k: v for k, v in registration_data.items() if k != 'password'})}}}"
            )
            return None, serializer, False

    @staticmethod
    def create_oauth2_application(user: User) -> Tuple[Optional[Application], bool]:
        """
        Create OAuth2 application for the user
        Uses thread-local storage to automatically get the unique ID.

        Args:
            user: User instance

        Returns:
            Tuple of (application: Optional[Application], success: bool)
        """
        try:
            # Get role-specific frontend URL
            frontend_url = get_frontend_url_by_role(user.role)

            application = Application.objects.create(
                name=f"Agritram App - {user.name}",
                user=user,
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                client_id=f"agritram-{user.id}-{secrets.token_urlsafe(8)}",
                client_secret=secrets.token_urlsafe(32),
                redirect_uris=f"{frontend_url}/auth/callback/",
            )

            logger.info(
                f"OPERATION_INFO: OAUTH_APP_CREATION | message=Created OAuth2 application for user {user.email} | metadata="
                f"{{'user_email': '{user.email}', 'user_id': {user.id}, 'client_id': '{application.client_id}', 'application_name': '{application.name}', 'redirect_uris': '{application.redirect_uris}'}}"
            )

            return application, True

        except Exception as e:
            logger.error(
                f"OPERATION_INFO: OAUTH_APP_CREATION | message=Failed to create OAuth2 application for user {user.email} | metadata="
                f"{{'user_email': '{user.email}', 'user_id': {user.id}, 'error': '{str(e)}', 'error_type': '{e.__class__.__name__}'}}"
            )
            return None, False

    @staticmethod
    def log_user_registration_event(
        user: User,
        device_data: Dict[str, Any],
        oauth2_app_created: bool,
        device_registered: bool,
        fingerprint: str,
    ) -> None:
        """
        Log comprehensive user registration business event

        Args:
            user: User instance
            device_data: Device information dictionary
            oauth2_app_created: Whether OAuth2 app was created successfully
            device_registered: Whether device was registered successfully
            fingerprint: Device fingerprint
        """
        logger.info(
            f"BUSINESS_EVENT: USER_REGISTERED | description=New user registration completed successfully | entity_type=USER | entity_id={user.id} | metadata={json.dumps({'user_id': user.id, 'email': user.email, 'name': user.name, 'role': user.role, 'device_name': device_data['device_name'], 'device_id': device_data['device_id'], 'device_type': device_data['device_type'], 'fingerprint': fingerprint, 'oauth2_app_created': oauth2_app_created, 'device_registered': device_registered})}"
        )

    @classmethod
    def handle_user_creation_flow(
        cls, request, device_data: Dict[str, Any], fingerprint: str
    ) -> Dict[str, Any]:
        """
        Handle the complete user creation flow

        Args:
            request: HTTP request object
            device_data: Device information dictionary
            fingerprint: Device fingerprint

        Returns:
            Dict containing user creation results
        """
        # Prepare registration data
        registration_data = cls.prepare_registration_data(request)

        # Create user
        user, serializer, is_valid = cls.create_user(registration_data)

        if not is_valid:
            return {
                "success": False,
                "user": None,
                "serializer": serializer,
                "oauth2_application": None,
                "error_response": handle_registration_serializer_errors(serializer),
            }

        # Create OAuth2 application
        oauth2_application, oauth2_app_created = cls.create_oauth2_application(user)

        return {
            "success": True,
            "user": user,
            "serializer": serializer,
            "oauth2_application": oauth2_application,
            "oauth2_app_created": oauth2_app_created,
            "registration_data": registration_data,
        }
